import axios from 'axios'

// 获取所有程序
export function listExePrograms(query) {
  return axios.get('/api/exeprogram/search', {
    params: {
      query: query
    }
  })
}

// 创建或更新程序
export function createOrUpdateExeProgram(formData) {
  return axios.post('/api/exeprogram', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除程序
export function deleteExeProgram(id) {
  return axios.delete(`/api/exeprogram/${id}`)
}

// 获取资源列表（资源池和资源机）
export function getResources() {
  return axios.get('/api/exeprogram/resources')
} 

/**
 * 上传大文件（支持10GB+的ZIP文件）
 * @param {File} file 文件对象
 * @param {Object} formData 完整的表单数据对象
 * @param {Function} onProgress 上传进度回调函数，参数为进度百分比
 * @returns {Promise} 上传结果的Promise
 */
export function uploadLargeFile(file, formData, onProgress) {
  const data = new FormData();
  
  // 添加文件
  data.append('file', file);
  
  // 添加所有表单字段
  if (formData) {
    Object.keys(formData).forEach(key => {
      if (key !== 'ProgramPackageFile' && formData[key] !== null && formData[key] !== undefined) {
        // 特别处理ID字段，确保编辑操作时ID能正确传递
        if (key === 'id' || key === 'ID') {
          data.append('ID', formData[key]);
        }
        // 确保布尔值被正确转换为字符串
        else if (typeof formData[key] === 'boolean') {
          data.append(key, formData[key] ? 'true' : 'false');
        } else {
          data.append(key, formData[key]);
        }
      }
    });
  }

  return axios.post('/api/exeprogram/upload-large-file', data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (progressEvent.lengthComputable && onProgress) {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(percentCompleted);
      }
    },
    // 增加超时时间，避免大文件上传超时
    timeout: 3600000 // 1小时
  });
} 