﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using AutoUpdaterDotNET;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Win32;
using RPASystem.Client.Common;
using RPASystem.Client.View;
using RPASystem.ClientWin.ISRPA.RuntimeEnv;
using RPASystem.ClientWin.Services.VOpenDir;
using Sunny.UI;

namespace RPASystem.Client
{
    public partial class MainForm : UIForm
    {
        ConnectionManager connectionManager = new ConnectionManager();
        // 添加两个图标字段
        private Icon defaultIcon;
        private Icon runningIcon;

        public MainForm()
        {
            InitializeComponent();
            InitializeTaskGrids();
            MsgTips.Initialize();

            // 初始化图标
            defaultIcon = notifyIcon1.Icon;
            runningIcon = Properties.Resources.c_running;
            // 提示
            UIToolTip toolTip = new UIToolTip() { InitialDelay = 100, AutoPopDelay = 10000 };// 鼠标悬停多久后显示提示，默认500毫秒// 提示显示时间，默认5000毫秒
            ulbLog.MouseDown += (s, e) =>
            {
                // 获取鼠标位置对应的项索引
                int index = ulbLog.IndexFromPoint(e.Location);
                if (index != ListBox.NoMatches)  // 如果鼠标在某一项上
                {
                    string itemText = ulbLog.Items[index].ToString();
                    toolTip.SetToolTip(ulbLog, itemText);
                }
            };
            // 连接日志
            connectionManager.OnLog += message =>
            {
                if (InvokeRequired)
                    Invoke(new Action(() => ulbLog.Items.Insert(0, message)));
                else
                    ulbLog.Items.Insert(0, message);

                if (ulbLog.Items.Count >= 100)
                    Invoke(new Action(() => ulbLog.Items.RemoveAt(ulbLog.Items.Count - 1)));
            };
        }

        private async void MainForm_Load(object sender, EventArgs e)
        {
            // 检测如果已经有一个实例在运行，则退出当前实例
            Process current = Process.GetCurrentProcess();
            Process[] processes = Process.GetProcessesByName(current.ProcessName);
            if (processes.Length > 1)
            {
                // MessageBox.Show("程序已运行，请勿重复启动！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                Application.Exit();
                return;
            }
            PreventLockDisplayUtils.PreventSleep(true);
            RegisterHotKey(this.Handle, HOTKEY_ID, 0, VK_F9);
            txtServerIP.Text = MachineMan.ServerIP;
            txtName.Text = MachineMan.GetHostname();
            var versionFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Version.dat");
            // 初始化版本号
            if (!File.Exists(versionFilePath))
            {
                File.WriteAllText(versionFilePath, "*******");
            }
            MachineMan.Version = File.ReadAllText(versionFilePath).Trim();
            this.Text = this.Text + MachineMan.Version;
            cbAutoRun.Checked = MachineMan.AutoRun;

            // 订阅任务状态变化事件
            connectionManager.OnTaskRunningStateChanged += isRunning => SetNotifyIcon(isRunning);
            connectionManager.OnConnectionRefused += () =>
            {
                this.Invoke(new Action(async () =>
                {
                    await DisCon();
                    this.ShowErrorTip("资源名已被注册!");
#if DEBUG
                    // 自动命名新名并连接
                    this.txtName.Text = DateTime.Now.ToString("yyyy-MM-dd-HH-mm-ss-fff");
                    await Con();
#endif
                }));
            };
            AutoStartAndHide();
            //MsgTips.Show("123123123123123213","www.qq.com");
            //await Task.Run(async () => { await Task.Delay(10000); MsgTips.Show("111111111111111111", "www.qq.com"); });
            VOpenDir.EnsureInstalled();// 安装协议
            await Task.Run(() => RPARuntimeEnv.Update());
            await Con();
            //- 启动时，在桌面创建一个快捷方式
            ShortcutUtils.CreateDesktopShortcut();
        }



        // 自动启动和隐藏窗口
        void AutoStartAndHide()
        {
            var shouldHide = false;


            // 判断是否存在-hide参数
            var args = Environment.GetCommandLineArgs();
            if (args.Contains("-hide"))
            {
                shouldHide = true;
            }

            // 如果有环境变量，说明是从更新器启动的
            string hideEnv = Environment.GetEnvironmentVariable("RPASystem_HIDE", EnvironmentVariableTarget.User);
            if (hideEnv == "true")
            {
                shouldHide = true;
                // 清除环境变量，避免影响下次启动
                Environment.SetEnvironmentVariable("RPASystem_HIDE", null, EnvironmentVariableTarget.User);
            }

            // 在所有初始化完成后，如果需要隐藏窗口，则隐藏
            if (shouldHide)
            {
                BeginInvoke(new Action(() => Hide()));
            }
        }

        private async void btnCon_Click(object sender, EventArgs e)
        {
            MachineMan.SetHostname(txtName.Text);
            MachineMan.ServerIP = txtServerIP.Text;
            await Con();
        }


        private async void btnExit_Click(object sender, EventArgs e)
        {
            await DisCon();
        }

        async Task Con()
        {
            btnCon.Enabled = false;
            btnExit.Enabled = true;
            txtName.Enabled = false;
            txtServerIP.Enabled = false;
            btnCon.Text = "连接中...";
            try
            {
                await connectionManager.StartConnectionAsync(MachineMan.BaseUrl, txtName.Text);
                if (connectionManager.GetConnection() != null && connectionManager.GetConnection().State == HubConnectionState.Connected)
                {
                    ResourceReporter.Instance.ReportResourceInfo(connectionManager.GetConnection(), txtName.Text);
                    btnCon.Text = "已连接";
                    return;
                }
            }
            catch (Exception ex)
            {
                this.ShowErrorTip(ex.Message);
            }
            btnCon.Text = "连接";
        }

        async Task DisCon()
        {
            btnCon.Enabled = true;
            txtName.Enabled = true;
            txtServerIP.Enabled = true;
            btnCon.Text = "连接";
            await connectionManager.Stop();
            btnExit.Enabled = false;
        }
        bool isExitingApplication = false;  // 添加一个标志来标识是否是真正退出
        private void 退出ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            isExitingApplication = true;
            Application.Exit();
        }

        private void notifyIcon1_DoubleClick(object sender, EventArgs e)
        {
            this.Show();
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing && !isExitingApplication)
            {
                e.Cancel = true;
                this.Hide();
            }
            else
            {
                notifyIcon1.Visible = false;
                UnregisterHotKey(this.Handle, HOTKEY_ID);
            }
        }

        private void notifyIcon1_MouseClick(object sender, MouseEventArgs e)
        {
            this.Show();
        }

        private void uiLabel9_Click(object sender, EventArgs e)
        {
            Process.Start(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs"));
        }

        private void notifyIcon1_Click(object sender, EventArgs e)
        {
            this.Show();
        }

        private void notifyIcon1_DoubleClick_1(object sender, EventArgs e)
        {
            this.Show();
        }

        // 热键ID
        private const int HOTKEY_ID = 9000;
        // F9的虚拟键码
        private const uint VK_F9 = 0x78;

        protected override void WndProc(ref Message m)
        {
            // 热键消息
            const int WM_HOTKEY = 0x0312;

            if (m.Msg == WM_HOTKEY && m.WParam.ToInt32() == HOTKEY_ID)
            {
                if (this.Visible)
                    this.Hide();
                else
                    this.Show();
            }

            base.WndProc(ref m);
        }

        // 添加以下代码到类的顶部，用于注册热键
        [DllImport("user32.dll")]
        private static extern bool RegisterHotKey(IntPtr hWnd, int id, uint fsModifiers, uint vk);

        [DllImport("user32.dll")]
        private static extern bool UnregisterHotKey(IntPtr hWnd, int id);

        // 添加设置图标的方法
        private void SetNotifyIcon(bool isRunning)
        {
            notifyIcon1.Icon = isRunning ? runningIcon : defaultIcon;
            notifyIcon1.Text = isRunning ? "大助手(运行中)" : "大助手";
        }

        private void InitializeTaskGrids()
        {
            // 订阅任务相关事件
            connectionManager.OnTaskAdded += task => AddTaskToRunningGrid(task);
            connectionManager.OnTaskCompleted += task => UpdateTaskInRunningGrid(task);
            connectionManager.OnTaskRemoved += taskId => RemoveTaskFromRunningGrid(taskId);

            // 注册Tab切换事件
            uiTabControl1.SelectedIndexChanged += (s, e) =>
            {
                if (uiTabControl1.SelectedTab == tabPage2) // 已完成任务列表
                {
                    LoadCompletedTasks();
                }
            };

            // 添加停止任务的点击事件处理
            dataGridViewRunningTask.CellContentClick += (s, e) =>
            {
                if (e.ColumnIndex == dataGridViewRunningTask.Columns["StopTask"].Index && e.RowIndex >= 0)
                {
                    var row = dataGridViewRunningTask.Rows[e.RowIndex];
                    if (row.Tag is JobInfoModel jobInfo)
                    {
                        connectionManager.HandleStopTask(jobInfo.Id);
                    }
                }
            };

            // 设置表格样式
            dataGridViewRunningTask.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            dataGridViewRunningTask.AllowUserToAddRows = false;
            dataGridViewRunningTask.AllowUserToDeleteRows = false;
            dataGridViewRunningTask.ReadOnly = true;
            dataGridViewRunningTask.SelectionMode = DataGridViewSelectionMode.FullRowSelect;

            uiDataGridView1.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            uiDataGridView1.AllowUserToAddRows = false;
            uiDataGridView1.AllowUserToDeleteRows = false;
            uiDataGridView1.ReadOnly = true;
            uiDataGridView1.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
        }

        private void AddTaskToRunningGrid(JobInfoModel task)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => AddTaskToRunningGrid(task)));
                return;
            }

            var row = dataGridViewRunningTask.Rows[dataGridViewRunningTask.Rows.Add()];
            UpdateGridRow(row, task);
        }

        private void UpdateTaskInRunningGrid(JobInfoModel task)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => UpdateTaskInRunningGrid(task)));
                return;
            }

            foreach (DataGridViewRow row in dataGridViewRunningTask.Rows)
            {
                if (row.Tag is JobInfoModel jobInfo && jobInfo.Id == task.Id)
                {
                    UpdateGridRow(row, task);
                    break;
                }
            }
        }

        private void RemoveTaskFromRunningGrid(long taskId)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => RemoveTaskFromRunningGrid(taskId)));
                return;
            }

            for (int i = dataGridViewRunningTask.Rows.Count - 1; i >= 0; i--)
            {
                var row = dataGridViewRunningTask.Rows[i];
                if (row.Tag is JobInfoModel jobInfo && jobInfo.Id == taskId)
                {
                    dataGridViewRunningTask.Rows.RemoveAt(i);
                    break;
                }
            }
        }

        private void LoadCompletedTasks()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(LoadCompletedTasks));
                return;
            }

            uiDataGridView1.Rows.Clear();
            var history = TaskHistoryManager.LoadHistory();

            foreach (var task in history)
            {
                var row = uiDataGridView1.Rows[uiDataGridView1.Rows.Add()];
                row.Cells["dataGridViewTextBoxColumn2"].Value = task.JobTaskName;
                row.Cells["dataGridViewTextBoxColumn4"].Value = task.ProgramName;
                row.Cells["dataGridViewTextBoxColumn3"].Value = task.ProgramVersion; // 程序版本
                row.Cells["dataGridViewTextBoxColumn5"].Value = task.StartTime?.ToString("yyyy-MM-dd HH:mm:ss");
                row.Cells["Column5"].Value = task.EndTime?.ToString("yyyy-MM-dd HH:mm:ss");
                row.Cells["Column6"].Value = task.Status.ToString();
            }
        }

        private void UpdateGridRow(DataGridViewRow row, JobInfoModel task)
        {
            row.Tag = task;
            row.Cells["TaskName"].Value = task.JobTaskName;
            row.Cells["ProgramName"].Value = task.ProgramName;
            row.Cells["ProgramVersion"].Value = task.ProgramVersion; // 程序版本
            row.Cells["StartTime"].Value = task.StartTime?.ToString("yyyy-MM-dd HH:mm:ss");
            row.Cells["StopTask"].Value = "停止";
        }

        private void cbAutoRun_CheckedChanged(object sender, EventArgs e)
        {
            MachineMan.AutoRun = cbAutoRun.Checked;
        }
    }
}
