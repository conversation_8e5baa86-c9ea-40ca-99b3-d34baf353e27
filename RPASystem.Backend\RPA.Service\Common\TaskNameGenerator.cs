using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using RPASystem.Model;
using Infrastructure.Attribute;

public interface ITaskNameGenerator
{
    Task<string> GenerateMainTaskNameAsync(string typePrefix = "RPA_");
    Task<string> GenerateSubTaskNameAsync(string parentTaskName);
}

[AppService(ServiceType = typeof(ITaskNameGenerator), ServiceLifetime = LifeTime.Scoped)]
public class TaskNameGenerator : ITaskNameGenerator
{
    private readonly RPASystemDbContext dbContext;
    
    public TaskNameGenerator(RPASystemDbContext dbContext)
    {
        this.dbContext = dbContext;
    }

    public async Task<string> GenerateMainTaskNameAsync(string typePrefix = "RPA_")
    {
        string dateStr = DateTime.Now.ToString("yyyyMMdd");
        
        using var transaction = await dbContext.Database.BeginTransactionAsync();
        try
        {
            string newTaskName;
            //// 先判断是否有当天的任务，只基于日期部分查询，不依赖前缀
            //bool hasTodayTask = await dbContext.JobTasks
            //    .AnyAsync(t => t.TaskName.Contains("-" + dateStr));

            //// 如果没有当天任务，直接返回001序号
            //if (!hasTodayTask)
            //{
            //    newTaskName = $"{typePrefix}{dateStr}001";
            //    await transaction.CommitAsync();
            //    return newTaskName;
            //}

            // 如果有当天任务，查询含有当天日期且ID最大的父任务
            var latestTask = await dbContext.JobTasks
                .Where(t => t.TaskName.Contains("_" + dateStr))
                .OrderByDescending(t => t.ID)  // 使用ID排序，获取最新创建的任务
                .Select(t => t.TaskName)
                .FirstOrDefaultAsync();
                
            int sequence = 1;
            if (latestTask != null)
            {
                // 尝试提取数字部分
                int digitStart = latestTask.IndexOf(dateStr) + dateStr.Length;
                if (digitStart < latestTask.Length)
                {
                    string numPart = latestTask.Substring(digitStart);
                    if (int.TryParse(numPart, out int currentSequence))
                    {
                        sequence = currentSequence + 1;
                    }
                }
            }
            else
            {
                newTaskName = $"{typePrefix}{dateStr}001";
                await transaction.CommitAsync();
                return newTaskName;
            }

            if (sequence > 999999)
            {
                throw new Exception("当天任务序号已超过最大值(999999)");
            }

            // 根据序列号的位数决定格式
            string format = sequence < 1000 ? "D3" : "D" + sequence.ToString().Length;
            newTaskName = $"{typePrefix}{dateStr}{sequence.ToString(format)}";
            await transaction.CommitAsync();
            return newTaskName;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task<string> GenerateSubTaskNameAsync(string parentTaskName)
    {
        using var transaction = await dbContext.Database.BeginTransactionAsync(System.Data.IsolationLevel.Serializable);
        try
        {
            // 获取所有直接子任务，使用悲观锁
            var subTasks = await dbContext.JobTasks
                .FromSqlRaw(@"
                    SELECT * 
                    FROM JobTasks 
                    WHERE TaskName REGEXP {0}
                    AND TaskName NOT REGEXP {1}
                    FOR UPDATE", 
                    $"^{parentTaskName}-[0-9]+$",  // 匹配直接子任务
                    $"^{parentTaskName}-[0-9]+-")  // 排除孙子任务
                .Select(t => t.TaskName)
                .ToListAsync();

            int sequence = 1;
            if (subTasks.Any())
            {
                // 提取所有数字后缀并找到最大值
                var maxSequence = subTasks
                    .Select(name => 
                    {
                        var lastPart = name.Split('-').Last();
                        return int.TryParse(lastPart, out int seq) ? seq : 0;
                    })
                    .Max();
                sequence = maxSequence + 1;
            }

            string newTaskName = $"{parentTaskName}-{sequence}";
            await transaction.CommitAsync();
            return newTaskName;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
}
