﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Net.Http;
using System.Net;

namespace RPASystem.Backend.WinTest
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
            // 初始化ListView控件
            InitListView();
        }

        private void InitListView()
        {
            // 清空listView1的列
            listView1.Columns.Clear();
            // 设置View为详细信息视图
            listView1.View = View.Details;
            // 添加列
            listView1.Columns.Add("序号", 80);
            listView1.Columns.Add("请求结果", 700);
        }

        private async void btnStart_Click(object sender, EventArgs e)
        {
            string url = "http://10.73.133.196:8888/api/Orchestration/create";
            string par = $"n={txtPName.Text} {txtPar.Text}";
            int count = Convert.ToInt32(txtCount.Text);
            //> 并发post请求url ，参数=par，并发数量=count
            //> 将返回结果显示在listView1  ，显示格式： 第i次请求返回结果：{}   {"成功!" 或 "失败!"}

            // 清空之前的列表
            listView1.Items.Clear();

            // 创建HttpClient实例
            using (HttpClient client = new HttpClient())
            {
                // 创建并发任务列表
                List<Task<Tuple<int, string, bool>>> tasks = new List<Task<Tuple<int, string, bool>>>();
                
                // 创建并发任务
                for (int i = 1; i <= count; i++)
                {
                    int index = i; // 捕获循环变量
                    tasks.Add(Task.Run(async () => 
                    {
                        try
                        {
                            // 每个请求都新建StringContent，避免复用导致异常
                            using (var content = new StringContent(par, Encoding.UTF8, "application/json"))
                            {
                                // 发送POST请求
                                HttpResponseMessage response = await client.PostAsync(url, content);
                                // 获取响应内容
                                string responseBody = await response.Content.ReadAsStringAsync();
                                // 判断请求是否成功
                                bool isSuccess = response.IsSuccessStatusCode;
                                return new Tuple<int, string, bool>(index, responseBody, isSuccess);
                            }
                        }
                        catch (Exception ex)
                        {
                            // 请求异常
                            return new Tuple<int, string, bool>(index, ex.Message, false);
                        }
                    }));
                }
                
                // 等待所有任务完成，然后按序号排序显示结果
                var results = await Task.WhenAll(tasks);
                
                // 按序号排序
                var sortedResults = results.OrderBy(r => r.Item1).ToList();
                
                // 在UI线程上更新ListView
                foreach (var result in sortedResults)
                {
                    // 创建ListViewItem并添加到ListView
                    ListViewItem item = new ListViewItem(result.Item1.ToString());
                    string status = result.Item3 ? "成功!" : "失败!";
                    item.SubItems.Add($"第{result.Item1}次请求返回结果：{result.Item2}   {status}");
                    listView1.Items.Add(item);
                }
            }
        }
    }
}
