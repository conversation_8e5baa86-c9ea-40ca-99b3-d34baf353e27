using RPASystem.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace RPASystem.Service
{
    public interface IExeProgramService
    {
        public IEnumerable<object> GetAllExeProgramsForList();
        public ExeProgram GetExeProgramById(long id);
        public void SaveExeProgram(ExeProgram exeProgram, IFormFile? programPackageFile = null, string? tempFilePath = null);
        public void DeleteExeProgram(long id);
        public IEnumerable<object> SearchExePrograms(string query);
        public ExeProgram GetExeProgramByName(string programName);
        public object GetAllResources();
        public byte[] DownloadExeProgram(string programName);
    }
}
