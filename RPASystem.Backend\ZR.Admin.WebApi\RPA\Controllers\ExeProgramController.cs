using Microsoft.AspNetCore.Mvc;
using RPASystem.Service;
using RPASystem.Model;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Net.Http.Headers;
using System.IO;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Http;
namespace RPASystem.WebApi.Controllers
{
    /// <summary>
    /// EXE程序管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ExeProgramController : ControllerBase
    {
        private readonly IExeProgramService exeProgramService;

        public ExeProgramController(IExeProgramService exeProgramService)
        {
            this.exeProgramService = exeProgramService;
        }

        /// <summary>
        /// 获取所有EXE程序，用于下拉框
        /// </summary>
        [HttpGet("GetAllExeProgramsForList")] // 添加具体的路由路径
        public IActionResult GetAllExeProgramsForList()
        {
            try
            {
                var programs = exeProgramService.GetAllExeProgramsForList();
                return Ok(programs);  // 直接返回数据，因为Service层已经处理好了格式
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    success = false,
                    message = ex.Message
                });
            }
        }

        /// <summary>
        /// 根据ID获取EXE程序
        /// </summary>
        [HttpGet("{id:long}")]
        public IActionResult GetExeProgram(long id)
        {
            var program = exeProgramService.GetExeProgramById(id);
            if (program == null)
            {
                return NotFound();
            }
            return Ok(program);
        }

        public class ExeProgramForm : ExeProgram
        {
            public IFormFile? ProgramPackageFile { get; set; }
        }

        /// <summary>
        /// 创建或更新EXE程序
        /// </summary>
        [HttpPost]
        public IActionResult SaveExeProgram([FromForm] ExeProgramForm form)
        {
            try
            {
                exeProgramService.SaveExeProgram(form, form.ProgramPackageFile);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// 使用流式传输上传大文件（支持10GB+的ZIP文件）
        /// </summary>
        /// <returns>上传结果</returns>
        [HttpPost("upload-large-file")]
        [DisableRequestSizeLimit] // 解除此Action的请求大小限制
        [RequestFormLimits(MultipartBodyLengthLimit = long.MaxValue, ValueLengthLimit = int.MaxValue)] // 进一步确保表单限制也被放宽
        public async Task<IActionResult> UploadLargeFile()
        {
            // 1. 检查请求是否为 multipart/form-data 类型
            if (!Request.HasFormContentType ||
                !MediaTypeHeaderValue.TryParse(Request.ContentType, out var mediaTypeHeader) ||
                string.IsNullOrEmpty(mediaTypeHeader.Boundary.Value))
            {
                return new UnsupportedMediaTypeResult();
            }

            var boundary = HeaderUtilities.RemoveQuotes(mediaTypeHeader.Boundary.Value).Value;
            var reader = new MultipartReader(boundary, Request.Body);

            // 2. 循环读取请求中的每一个 section (一部分)
            var section = await reader.ReadNextSectionAsync();
            string savedFilePath = string.Empty;
            string originalFileName = string.Empty;

            // 创建一个ExeProgram对象来存储所有表单字段
            var exeProgram = new ExeProgram
            {
                ProgramType = ProgramTypeEnum.EXE, // 默认为EXE类型
                IsExclusive = false,
                InputParameters = "[]",
                ResourceSelection = "",
                NotificationResourceMachines = ""
            };

            while (section != null)
            {
                // 3. 判断这个 section 是否是文件部分
                var hasContentDispositionHeader = ContentDispositionHeaderValue.TryParse(section.ContentDisposition, out var contentDisposition);

                if (hasContentDispositionHeader && contentDisposition.DispositionType.Equals("form-data"))
                {
                    // 处理表单字段
                    var fieldName = contentDisposition.Name.Value.Trim('"');

                    // 处理文件上传
                    if (!string.IsNullOrEmpty(contentDisposition.FileName.Value))
                    {
                        // 获取文件名
                        originalFileName = Path.GetFileName(contentDisposition.FileName.Value);
                        var fileExtension = Path.GetExtension(originalFileName);

                        // 检查文件扩展名
                        if (!string.Equals(fileExtension, ".zip", StringComparison.OrdinalIgnoreCase))
                        {
                            return BadRequest(new { success = false, message = "只支持上传ZIP文件" });
                        }

                        // 在这里你可以定义文件的存储路径，比如一个临时文件夹或专门的上传目录
                        var uploadPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "UploadTemp");
                        if (!Directory.Exists(uploadPath))
                        {
                            Directory.CreateDirectory(uploadPath);
                        }

                        var uniqueFileName = Guid.NewGuid().ToString() + fileExtension;
                        savedFilePath = Path.Combine(uploadPath, uniqueFileName);

                        // 4. 创建文件流，并将section的流内容拷贝进去
                        using (var fileStream = new FileStream(savedFilePath, FileMode.Create, FileAccess.Write, FileShare.None, 81920, useAsync: true))
                        {
                            await section.Body.CopyToAsync(fileStream);
                        }
                    }
                    // 处理其他表单字段 - 将值保存到exeProgram对象中
                    else
                    {
                        using (var streamReader = new StreamReader(section.Body))
                        {
                            var value = await streamReader.ReadToEndAsync();

                            // 根据字段名设置exeProgram对象的属性
                            switch (fieldName)
                            {
                                case "ID":
                                case "id":
                                    if (long.TryParse(value, out long id) && id > 0)
                                        exeProgram.ID = id;
                                    break;
                                case "programName":
                                    exeProgram.ProgramName = value;
                                    break;
                                case "programType":
                                    if (int.TryParse(value, out int programType))
                                        exeProgram.ProgramType = (ProgramTypeEnum)programType;
                                    break;
                                case "isExclusive":
                                    if (bool.TryParse(value, out bool isExclusive))
                                        exeProgram.IsExclusive = isExclusive;
                                    break;
                                case "inputParameters":
                                    exeProgram.InputParameters = string.IsNullOrEmpty(value) ? null : value;
                                    break;
                                case "resourceSelection":
                                    exeProgram.ResourceSelection = value;
                                    break;
                                case "notificationResourceMachines":
                                    exeProgram.NotificationResourceMachines = value;
                                    break;
                                case "remarks":
                                    exeProgram.Remarks = value;
                                    break;
                                case "version":
                                    exeProgram.Version = value;
                                    break;
                                    // 可以根据需要添加更多字段
                            }
                        }
                    }
                }

                // 读取下一个 section
                section = await reader.ReadNextSectionAsync();
            }

            // 5. 处理上传后的文件
            if (string.IsNullOrEmpty(savedFilePath))
            {
                return BadRequest(new { success = false, message = "请求中未找到文件" });
            }

            // 检查程序名是否为空
            if (string.IsNullOrEmpty(exeProgram.ProgramName))
            {
                // 如果没有提供程序名，则使用文件名作为程序名（去掉.zip后缀）
                exeProgram.ProgramName = Path.GetFileNameWithoutExtension(originalFileName);
            }

            try
            {
                // 调用服务层方法保存程序，传递临时文件路径而不是IFormFile
                exeProgramService.SaveExeProgram(exeProgram, null, savedFilePath);

                // 返回成功信息
                return Ok(new
                {
                    success = true,
                    message = exeProgram.ID > 0 ? "程序更新成功" : "程序创建成功",
                    data = new
                    {
                        id = exeProgram.ID,
                        programName = exeProgram.ProgramName,
                        version = exeProgram.Version
                    }
                });
            }
            catch (Exception ex)
            {
                // 如果处理失败，删除临时文件
                if (System.IO.File.Exists(savedFilePath))
                {
                    try
                    {
                        System.IO.File.Delete(savedFilePath);
                    }
                    catch { /* 忽略删除临时文件时的错误 */ }
                }

                return BadRequest(new { success = false, message = $"文件上传失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 删除EXE程序
        /// </summary>
        [HttpDelete("{id}")]
        public IActionResult DeleteExeProgram(long id)
        {
            exeProgramService.DeleteExeProgram(id);
            return Ok();
        }

        /// <summary>
        /// 搜索EXE程序
        /// </summary>
        [HttpGet("search")]
        public IActionResult SearchExePrograms([FromQuery] string? query = null) // 修改参数为可空，并提供默认值
        {
            try
            {
                var exePrograms = exeProgramService.SearchExePrograms(query ?? string.Empty); // 如果query为null，使用空字符串
                return Ok(exePrograms);
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    success = false,
                    message = ex.Message
                });
            }
        }

        [HttpGet("resources")]
        public IActionResult GetResources()
        {
            try
            {
                var resources = exeProgramService.GetAllResources();
                return Ok(resources);
            }
            catch (Exception ex)
            {
                return BadRequest($"获取资源列表失败: {ex.Message}");
            }
        }

        [HttpGet("download/{programName}")]
        public IActionResult DownloadExeProgram(string programName)
        {
            var programPackage = exeProgramService.DownloadExeProgram(programName);
            if (programPackage == null)
            {
                return NotFound($"未找到程序包: {programName}");
            }
            return File(programPackage, "application/octet-stream", $"{programName}.zip");
        }
    }
}
